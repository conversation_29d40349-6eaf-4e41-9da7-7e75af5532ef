/**
 * WhiskerGuard AI Service - AI请求上下文支持 JDL 定义
 *
 * 本文件用于为 AiRequest 实体添加对话上下文支持功能
 * 添加字段：conversationId（对话会话ID）和 conversationSequence（对话序号）
 *
 * 使用方法：
 * jhipster jdl jdl/ai_request_context_update.jdl --force
 */

/**
 * 请求状态枚举
 */
enum RequestStatus {
    SUCCESS,     // 成功
    FAILED,      // 失败
    PROCESSING,  // 处理中
    TIMEOUT      // 超时
}

/**
 * 审核结果枚举
 */
enum ReviewResult {
    APPROVED,    // 通过
    REJECTED,    // 拒绝
    MODIFIED     // 需要修改
}

/**
 * AI请求（AiRequest）实体 - 增强版本，支持对话上下文
 */
entity AiRequest {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 职工（用户）ID */
    employeeId Long required,
    /** 工具类型 */
    toolType String required maxlength(64),
    /** 提示词 */
    prompt String required,
    /** 响应内容 */
    response String required,
    /** 请求时间 */
    requestTime Instant required,
    /** 响应时间 */
    responseTime Instant,
    /** 请求状态 */
    status RequestStatus required,
    /** 错误信息 */
    errorMessage String,
    /** 扩展元数据 */
    metadata String,
    /** 对话会话ID - 用于关联同一个对话的多轮交互 */
    conversationId String maxlength(64),
    /** 对话序号 - 在对话中的顺序，用于排序 */
    conversationSequence Integer,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * AI审核（AiReview）实体
 */
entity AiReview {
    /** 主键 ID */
    id Long,
    /** 租户 ID */
    tenantId Long required,
    /** 职工（用户）ID */
    employeeId Long required,
    /** 审核内容 */
    reviewContent String required,
    /** 审核结果 */
    reviewResult ReviewResult required,
    /** 审核日期 */
    reviewDate Instant required,
    /** 审核人 */
    reviewer String required maxlength(64),
    /** 审核意见 */
    comments String,
    /** 反馈数据 */
    feedback String,
    /** 扩展元数据 */
    metadata String,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String,
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String,
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/**
 * 实体关系定义
 */
relationship ManyToOne {
    AiRequest{tool} to AiTool with builtInEntity,
    AiReview{request} to AiRequest
}
