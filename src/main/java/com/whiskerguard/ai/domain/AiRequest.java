package com.whiskerguard.ai.domain;

import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * AI请求（AiRequest）实体
 */
@Entity
@Table(name = "ai_request")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AiRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 职工（用户）ID
     */
    @NotNull
    @Column(name = "employee_id", nullable = false)
    private Long employeeId;

    /**
     * 工具类型
     */
    @NotNull
    @Size(max = 64)
    @Column(name = "tool_type", length = 64, nullable = false)
    private String toolType;

    /**
     * 提示词
     */
    @NotNull
    @Column(name = "prompt", nullable = false, columnDefinition = "TEXT")
    private String prompt;

    /**
     * 响应内容
     */
    @NotNull
    @Column(name = "response", nullable = false, columnDefinition = "TEXT")
    private String response;

    /**
     * 请求时间
     */
    @NotNull
    @Column(name = "request_time", nullable = false)
    private Instant requestTime;

    /**
     * 响应时间
     */
    @Column(name = "response_time")
    private Instant responseTime;

    /**
     * 请求状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private RequestStatus status;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 扩展元数据
     */
    @Column(name = "metadata")
    private String metadata;

    /**
     * 对话会话ID - 用于关联同一个对话的多轮交互
     */
    @Size(max = 64)
    @Column(name = "conversation_id", length = 64)
    private String conversationId;

    /**
     * 对话序号 - 在对话中的顺序，用于排序
     */
    @Column(name = "conversation_sequence")
    private Integer conversationSequence;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    private AiTool tool;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public AiRequest id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public AiRequest tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return this.employeeId;
    }

    public AiRequest employeeId(Long employeeId) {
        this.setEmployeeId(employeeId);
        return this;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getToolType() {
        return this.toolType;
    }

    public AiRequest toolType(String toolType) {
        this.setToolType(toolType);
        return this;
    }

    public void setToolType(String toolType) {
        this.toolType = toolType;
    }

    public String getPrompt() {
        return this.prompt;
    }

    public AiRequest prompt(String prompt) {
        this.setPrompt(prompt);
        return this;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getResponse() {
        return this.response;
    }

    public AiRequest response(String response) {
        this.setResponse(response);
        return this;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Instant getRequestTime() {
        return this.requestTime;
    }

    public AiRequest requestTime(Instant requestTime) {
        this.setRequestTime(requestTime);
        return this;
    }

    public void setRequestTime(Instant requestTime) {
        this.requestTime = requestTime;
    }

    public Instant getResponseTime() {
        return this.responseTime;
    }

    public AiRequest responseTime(Instant responseTime) {
        this.setResponseTime(responseTime);
        return this;
    }

    public void setResponseTime(Instant responseTime) {
        this.responseTime = responseTime;
    }

    public RequestStatus getStatus() {
        return this.status;
    }

    public AiRequest status(RequestStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(RequestStatus status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public AiRequest errorMessage(String errorMessage) {
        this.setErrorMessage(errorMessage);
        return this;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public AiRequest metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public String getConversationId() {
        return this.conversationId;
    }

    public AiRequest conversationId(String conversationId) {
        this.setConversationId(conversationId);
        return this;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Integer getConversationSequence() {
        return this.conversationSequence;
    }

    public AiRequest conversationSequence(Integer conversationSequence) {
        this.setConversationSequence(conversationSequence);
        return this;
    }

    public void setConversationSequence(Integer conversationSequence) {
        this.conversationSequence = conversationSequence;
    }

    public Integer getVersion() {
        return this.version;
    }

    public AiRequest version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public AiRequest createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public AiRequest createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public AiRequest updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public AiRequest updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public AiRequest isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AiTool getTool() {
        return this.tool;
    }

    public void setTool(AiTool aiTool) {
        this.tool = aiTool;
    }

    public AiRequest tool(AiTool aiTool) {
        this.setTool(aiTool);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AiRequest)) {
            return false;
        }
        return getId() != null && getId().equals(((AiRequest) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AiRequest{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", toolType='" + getToolType() + "'" +
            ", prompt='" + getPrompt() + "'" +
            ", response='" + getResponse() + "'" +
            ", requestTime='" + getRequestTime() + "'" +
            ", responseTime='" + getResponseTime() + "'" +
            ", status='" + getStatus() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", conversationId='" + getConversationId() + "'" +
            ", conversationSequence=" + getConversationSequence() +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
