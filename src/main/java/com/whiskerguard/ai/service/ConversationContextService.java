/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ConversationContextService.java
 * 包    名：com.whiskerguard.ai.service
 * 描    述：对话上下文管理服务，负责处理多轮对话的上下文构建和管理
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/23
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service;

import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.mapper.AiRequestMapper;
import java.util.List;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对话上下文管理服务
 * <p>
 * 负责处理多轮对话的上下文构建、历史记录管理和对话会话管理。
 * 支持多租户环境下的对话隔离和上下文长度控制。
 */
@Service
@Transactional
public class ConversationContextService {

    private static final Logger log = LoggerFactory.getLogger(ConversationContextService.class);

    /**
     * 默认最大上下文轮数
     */
    private static final int DEFAULT_MAX_CONTEXT_TURNS = 100;

    /**
     * 上下文提示词模板
     */
    private static final String CONTEXT_TEMPLATE =
        "以下是我们之前的对话历史，请基于这些上下文回答我的新问题：\n\n%s\n用户: %s\n助手: ";

    private final AiRequestRepository aiRequestRepository;
    private final AiRequestMapper aiRequestMapper;

    /**
     * 构造函数
     *
     * @param aiRequestRepository AI请求数据访问层
     * @param aiRequestMapper AI请求实体映射器
     */
    public ConversationContextService(
        AiRequestRepository aiRequestRepository,
        AiRequestMapper aiRequestMapper
    ) {
        this.aiRequestRepository = aiRequestRepository;
        this.aiRequestMapper = aiRequestMapper;
    }

    /**
     * 生成新的对话会话ID
     *
     * @return 新的对话会话ID
     */
    public String generateConversationId() {
        return "conv-" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 获取对话历史上下文
     *
     * @param conversationId 对话会话ID
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param maxTurns 最大上下文轮数
     * @return 对话历史记录列表，按序号升序排列
     */
    @Transactional(readOnly = true)
    public List<AiRequestDTO> getConversationHistory(
        String conversationId,
        Long tenantId,
        Long employeeId,
        int maxTurns
    ) {
        log.debug("获取对话历史: conversationId={}, tenantId={}, employeeId={}, maxTurns={}",
            conversationId, tenantId, employeeId, maxTurns);

        if (conversationId == null || conversationId.trim().isEmpty()) {
            log.debug("对话ID为空，返回空历史记录");
            return List.of();
        }

        try {
            // 限制查询数量，避免获取过多历史记录
            Pageable pageable = PageRequest.of(0, Math.max(1, maxTurns));

            var historyPage = aiRequestRepository
                .findByConversationIdAndTenantIdAndEmployeeIdOrderByConversationSequenceAsc(
                    conversationId, tenantId, employeeId, pageable);

            var history = aiRequestMapper.toDto(historyPage.getContent());

            log.debug("成功获取对话历史，记录数: {}", history.size());
            return history;

        } catch (Exception e) {
            log.error("获取对话历史失败: conversationId={}, error={}", conversationId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 构建包含上下文的完整提示词
     *
     * @param currentPrompt 当前用户输入的提示词
     * @param history 对话历史记录
     * @return 包含上下文的完整提示词
     */
    public String buildContextualPrompt(String currentPrompt, List<AiRequestDTO> history) {
        log.debug("构建上下文提示词: currentPrompt长度={}, 历史记录数={}",
            currentPrompt != null ? currentPrompt.length() : 0, history.size());

        if (history == null || history.isEmpty()) {
            log.debug("无历史记录，返回原始提示词");
            return currentPrompt;
        }

        try {
            StringBuilder contextBuilder = new StringBuilder();

            // 构建历史对话上下文
            for (AiRequestDTO request : history) {
                if (request.getPrompt() != null && request.getResponse() != null) {
                    contextBuilder.append("用户: ").append(request.getPrompt()).append("\n");
                    contextBuilder.append("助手: ").append(request.getResponse()).append("\n\n");
                }
            }

            // 使用模板构建最终提示词
            String contextualPrompt = String.format(CONTEXT_TEMPLATE,
                contextBuilder.toString().trim(), currentPrompt);

            log.debug("成功构建上下文提示词，总长度: {}", contextualPrompt.length());
            return contextualPrompt;

        } catch (Exception e) {
            log.error("构建上下文提示词失败: {}", e.getMessage(), e);
            // 出错时返回原始提示词，确保服务可用性
            return currentPrompt;
        }
    }

    /**
     * 获取对话中的下一个序号
     *
     * @param conversationId 对话会话ID
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 下一个序号
     */
    @Transactional(readOnly = true)
    public Integer getNextSequence(String conversationId, Long tenantId, Long employeeId) {
        log.debug("获取下一个对话序号: conversationId={}, tenantId={}, employeeId={}",
            conversationId, tenantId, employeeId);

        if (conversationId == null || conversationId.trim().isEmpty()) {
            return 1; // 新对话从序号1开始
        }

        try {
            var maxSequence = aiRequestRepository
                .findMaxSequenceByConversationId(conversationId, tenantId, employeeId);

            int nextSequence = maxSequence.orElse(0) + 1;
            log.debug("下一个对话序号: {}", nextSequence);
            return nextSequence;

        } catch (Exception e) {
            log.error("获取下一个对话序号失败: conversationId={}, error={}", conversationId, e.getMessage(), e);
            return 1; // 出错时返回默认序号
        }
    }

    /**
     * 获取用户的对话列表
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 对话ID列表，按创建时间倒序
     */
    @Transactional(readOnly = true)
    public List<String> getUserConversations(Long tenantId, Long employeeId) {
        log.debug("获取用户对话列表: tenantId={}, employeeId={}", tenantId, employeeId);

        try {
            var conversations = aiRequestRepository
                .findConversationIdsByTenantIdAndEmployeeId(tenantId, employeeId);

            log.debug("成功获取用户对话列表，对话数: {}", conversations.size());
            return conversations;

        } catch (Exception e) {
            log.error("获取用户对话列表失败: tenantId={}, employeeId={}, error={}",
                tenantId, employeeId, e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * 验证上下文参数的有效性
     *
     * @param conversationId 对话会话ID
     * @param enableContext 是否启用上下文
     * @param maxContextTurns 最大上下文轮数
     * @return 是否应该启用上下文功能
     */
    public boolean shouldEnableContext(String conversationId, Boolean enableContext, Integer maxContextTurns) {
        // 必须同时满足：提供了对话ID 且 明确启用了上下文
        boolean shouldEnable = conversationId != null &&
                              !conversationId.trim().isEmpty() &&
                              Boolean.TRUE.equals(enableContext);

        log.debug("上下文启用检查: conversationId={}, enableContext={}, shouldEnable={}",
            conversationId, enableContext, shouldEnable);

        return shouldEnable;
    }

    /**
     * 获取有效的最大上下文轮数
     *
     * @param maxContextTurns 请求中的最大上下文轮数
     * @return 有效的最大上下文轮数
     */
    public int getEffectiveMaxContextTurns(Integer maxContextTurns) {
        if (maxContextTurns == null || maxContextTurns <= 0) {
            return DEFAULT_MAX_CONTEXT_TURNS;
        }
        // 限制最大值，避免上下文过长
        return Math.min(maxContextTurns, 50);
    }
}
