<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        为 AiRequest 实体添加对话上下文支持字段
        - conversation_id: 对话会话ID，用于关联同一个对话的多轮交互
        - conversation_sequence: 对话序号，在对话中的顺序，用于排序
    -->
    <changeSet id="20250623000000-1" author="yanhaishui">
        <addColumn tableName="ai_request">
            <column name="conversation_id" type="varchar(64)" remarks="对话会话ID - 用于关联同一个对话的多轮交互">
                <constraints nullable="true" />
            </column>
            <column name="conversation_sequence" type="integer" remarks="对话序号 - 在对话中的顺序，用于排序">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>

    <!--
        为新增字段创建索引以提升查询性能
    -->
    <changeSet id="20250623000000-2" author="yanhaishui">
        <!-- 为 conversation_id 创建索引，用于快速查询同一对话的所有记录 -->
        <createIndex indexName="idx_ai_request_conversation_id" tableName="ai_request">
            <column name="conversation_id"/>
        </createIndex>
        
        <!-- 为 conversation_id + tenant_id + employee_id 创建复合索引，用于多租户环境下的对话查询 -->
        <createIndex indexName="idx_ai_request_conversation_tenant_employee" tableName="ai_request">
            <column name="conversation_id"/>
            <column name="tenant_id"/>
            <column name="employee_id"/>
        </createIndex>
        
        <!-- 为 conversation_id + conversation_sequence 创建复合索引，用于对话历史排序 -->
        <createIndex indexName="idx_ai_request_conversation_sequence" tableName="ai_request">
            <column name="conversation_id"/>
            <column name="conversation_sequence"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
